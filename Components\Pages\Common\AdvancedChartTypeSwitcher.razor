@using ApexCharts

<!-- 進階圖表類型切換組件 -->
<div class="chart-type-switcher mb-3">
    <!-- 圖表類型選擇 -->
    <div class="text-center mb-2">
        <div class="btn-group" role="group">
            @foreach (var chartType in AvailableChartTypes)
            {
                <button type="button"
                        class="btn @(CurrentChartType == chartType.Type ? "btn-primary" : "btn-outline-primary")"
                        @onclick="async () => await OnChartTypeChanged(chartType.Type)">
                    <i class="@chartType.Icon"></i> @chartType.DisplayName
                </button>
            }
        </div>
    </div>

    <!-- 長條圖專用選項 -->
    @if (CurrentChartType == SeriesType.Bar && ShowBarOptions)
    {
        <div class="text-center">
            <div class="row">
                <div class="col-6">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="stackedSwitch"
                               checked="@IsStacked" @onchange="OnStackedChanged">
                        <label class="form-check-label" for="stackedSwitch">
                            <i class="fa-solid fa-layer-group"></i> 堆疊圖表
                        </label>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="horizontalSwitch"
                               checked="@IsHorizontal" @onchange="OnHorizontalChanged">
                        <label class="form-check-label" for="horizontalSwitch">
                            <i class="fa-solid fa-arrows-left-right"></i> 橫式圖表
                        </label>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 面積圖專用選項 -->
    @if (CurrentChartType == SeriesType.Area && ShowAreaOptions)
    {
        <div class="text-center">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="areaStackedSwitch"
                       checked="@IsStacked" @onchange="OnStackedChanged">
                <label class="form-check-label" for="areaStackedSwitch">
                    <i class="fa-solid fa-layer-group"></i> 堆疊面積圖
                </label>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public SeriesType CurrentChartType { get; set; } = SeriesType.Bar;
    [Parameter] public EventCallback<SeriesType> CurrentChartTypeChanged { get; set; }
    [Parameter] public List<ChartTypeOption> AvailableChartTypes { get; set; } = new();
    [Parameter] public EventCallback<SeriesType> OnChartTypeChange { get; set; }
    
    // 長條圖選項
    [Parameter] public bool ShowBarOptions { get; set; } = true;
    [Parameter] public bool IsStacked { get; set; } = false;
    [Parameter] public EventCallback<bool> IsStackedChanged { get; set; }
    [Parameter] public bool IsHorizontal { get; set; } = false;
    [Parameter] public EventCallback<bool> IsHorizontalChanged { get; set; }
    
    // 面積圖選項
    [Parameter] public bool ShowAreaOptions { get; set; } = true;
    
    // 事件回調
    [Parameter] public EventCallback<ChartOptionsChangeEventArgs> OnChartOptionsChanged { get; set; }

    private async Task OnChartTypeChanged(SeriesType newType)
    {
        if (CurrentChartType != newType)
        {
            CurrentChartType = newType;
            await CurrentChartTypeChanged.InvokeAsync(newType);
            await OnChartTypeChange.InvokeAsync(newType);
            await NotifyOptionsChanged();
        }
    }

    private async Task OnStackedChanged(ChangeEventArgs e)
    {
        IsStacked = (bool)e.Value;
        await IsStackedChanged.InvokeAsync(IsStacked);
        await NotifyOptionsChanged();
    }

    private async Task OnHorizontalChanged(ChangeEventArgs e)
    {
        IsHorizontal = (bool)e.Value;
        await IsHorizontalChanged.InvokeAsync(IsHorizontal);
        await NotifyOptionsChanged();
    }

    private async Task NotifyOptionsChanged()
    {
        var args = new ChartOptionsChangeEventArgs
        {
            ChartType = CurrentChartType,
            IsStacked = IsStacked,
            IsHorizontal = IsHorizontal
        };
        await OnChartOptionsChanged.InvokeAsync(args);
    }

    public class ChartTypeOption
    {
        public SeriesType Type { get; set; }
        public string DisplayName { get; set; } = "";
        public string Icon { get; set; } = "";

        public ChartTypeOption(SeriesType type, string displayName, string icon)
        {
            Type = type;
            DisplayName = displayName;
            Icon = icon;
        }
    }

    public class ChartOptionsChangeEventArgs
    {
        public SeriesType ChartType { get; set; }
        public bool IsStacked { get; set; }
        public bool IsHorizontal { get; set; }
    }

    // 預設的圖表類型選項
    public static List<ChartTypeOption> GetDefaultChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Line, "折線圖", "fa-solid fa-chart-line"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area")
        };
    }

    // 完整的圖表類型選項（包含所有支援的類型）
    public static List<ChartTypeOption> GetAllChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Line, "折線圖", "fa-solid fa-chart-line"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area"),
            new ChartTypeOption(SeriesType.Pie, "圓餅圖", "fa-solid fa-chart-pie"),
            new ChartTypeOption(SeriesType.Donut, "甜甜圈圖", "fa-solid fa-circle-notch"),
            new ChartTypeOption(SeriesType.RadialBar, "徑向條形圖", "fa-solid fa-chart-simple"),
            new ChartTypeOption(SeriesType.Radar, "雷達圖", "fa-solid fa-chart-radar"),
            new ChartTypeOption(SeriesType.PolarArea, "極地面積圖", "fa-solid fa-chart-area"),
            new ChartTypeOption(SeriesType.Treemap, "樹狀圖", "fa-solid fa-th"),
            new ChartTypeOption(SeriesType.Scatter, "散點圖", "fa-solid fa-braille")
        };
    }

    // 適合時間序列數據的圖表類型
    public static List<ChartTypeOption> GetTimeSeriesChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Line, "折線圖", "fa-solid fa-chart-line"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area"),
            new ChartTypeOption(SeriesType.Scatter, "散點圖", "fa-solid fa-braille")
        };
    }

    // 適合分類數據的圖表類型
    public static List<ChartTypeOption> GetCategoricalChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Pie, "圓餅圖", "fa-solid fa-chart-pie"),
            new ChartTypeOption(SeriesType.Donut, "甜甜圈圖", "fa-solid fa-circle-notch"),
            new ChartTypeOption(SeriesType.RadialBar, "徑向條形圖", "fa-solid fa-chart-simple"),
            new ChartTypeOption(SeriesType.Treemap, "樹狀圖", "fa-solid fa-th")
        };
    }

    // 只有長條圖和面積圖的選項
    public static List<ChartTypeOption> GetBarAndAreaChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area")
        };
    }

    protected override void OnInitialized()
    {
        // 如果沒有提供可用的圖表類型，使用預設值
        if (AvailableChartTypes == null || !AvailableChartTypes.Any())
        {
            AvailableChartTypes = GetDefaultChartTypes();
        }
    }
}
