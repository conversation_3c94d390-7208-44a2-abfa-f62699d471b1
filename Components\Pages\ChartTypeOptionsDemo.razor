@page "/chart-type-options-demo"
@using ApexCharts
@rendermode InteractiveServer

<PageTitle>圖表類型選項展示</PageTitle>
<MyPageTitle Title="圖表類型選項展示"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="alert alert-info">
                <h5><i class="fa-solid fa-info-circle"></i> 不同圖表類型組合展示</h5>
                <p>這個頁面展示了如何使用不同的圖表類型組合：</p>
                <ul>
                    <li><strong>長條圖 + 面積圖</strong> - 適合紙本圖書等數據</li>
                    <li><strong>基本圖表類型</strong> - 長條圖、折線圖、面積圖</li>
                    <li><strong>時間序列圖表</strong> - 長條圖、折線圖、面積圖、散點圖</li>
                    <li><strong>分類數據圖表</strong> - 長條圖、圓餅圖、甜甜圈圖等</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 長條圖 + 面積圖 -->
        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">長條圖 + 面積圖組合</h5>
                    <small class="text-muted">適合紙本圖書等數據展示</small>
                </div>
                <div class="card-body">
                    <AdvancedChartTypeSwitcher CurrentChartType="@barAreaChartType"
                                               AvailableChartTypes="@barAreaTypes"
                                               ShowBarOptions="true"
                                               ShowAreaOptions="true"
                                               IsStacked="@barAreaStacked"
                                               IsHorizontal="@barAreaHorizontal"
                                               OnChartOptionsChanged="@HandleBarAreaChartChange" />
                    
                    @if (shouldRenderBarAreaChart)
                    {
                        <ApexChart TItem="DemoData"
                                   Title="長條圖 + 面積圖展示"
                                   Options="barAreaChartOptions">
                            <ApexPointSeries TItem="DemoData"
                                             Items="demoData1"
                                             SeriesType="@barAreaChartType"
                                             Name="數據系列 1"
                                             XValue="@(e => e.Category)"
                                             YValue="@(e => e.Value)" />
                            <ApexPointSeries TItem="DemoData"
                                             Items="demoData2"
                                             SeriesType="@barAreaChartType"
                                             Name="數據系列 2"
                                             XValue="@(e => e.Category)"
                                             YValue="@(e => e.Value)" />
                        </ApexChart>
                    }
                </div>
            </div>
        </div>

        <!-- 基本圖表類型 -->
        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">基本圖表類型</h5>
                    <small class="text-muted">長條圖、折線圖、面積圖</small>
                </div>
                <div class="card-body">
                    <AdvancedChartTypeSwitcher CurrentChartType="@basicChartType"
                                               AvailableChartTypes="@basicTypes"
                                               ShowBarOptions="true"
                                               ShowAreaOptions="true"
                                               IsStacked="@basicStacked"
                                               IsHorizontal="@basicHorizontal"
                                               OnChartOptionsChanged="@HandleBasicChartChange" />
                    
                    @if (shouldRenderBasicChart)
                    {
                        <ApexChart TItem="DemoData"
                                   Title="基本圖表類型展示"
                                   Options="basicChartOptions">
                            <ApexPointSeries TItem="DemoData"
                                             Items="demoData1"
                                             SeriesType="@basicChartType"
                                             Name="基本數據"
                                             XValue="@(e => e.Category)"
                                             YValue="@(e => e.Value)" />
                        </ApexChart>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">使用方法說明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>1. 只要長條圖 + 面積圖</h6>
                            <pre><code>// 在 AzureChart 中使用
EnableAdvancedChartTypeSwitcher="true"
AdvancedAvailableChartTypes="@AdvancedChartTypeSwitcher.GetBarAndAreaChartTypes()"</code></pre>
                        </div>
                        <div class="col-md-6">
                            <h6>2. 其他預設組合</h6>
                            <ul>
                                <li><code>GetDefaultChartTypes()</code> - 基本三種</li>
                                <li><code>GetTimeSeriesChartTypes()</code> - 時間序列</li>
                                <li><code>GetCategoricalChartTypes()</code> - 分類數據</li>
                                <li><code>GetAllChartTypes()</code> - 所有類型</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 演示數據模型
    public class DemoData
    {
        public string Category { get; set; } = "";
        public decimal Value { get; set; }
    }

    // 演示數據
    private List<DemoData> demoData1 = new();
    private List<DemoData> demoData2 = new();

    // 長條圖 + 面積圖組合
    private SeriesType barAreaChartType = SeriesType.Bar;
    private bool shouldRenderBarAreaChart = true;
    private bool barAreaStacked = false;
    private bool barAreaHorizontal = false;
    private ApexChartOptions<DemoData> barAreaChartOptions = new();
    private List<AdvancedChartTypeSwitcher.ChartTypeOption> barAreaTypes = new();

    // 基本圖表類型
    private SeriesType basicChartType = SeriesType.Line;
    private bool shouldRenderBasicChart = true;
    private bool basicStacked = false;
    private bool basicHorizontal = false;
    private ApexChartOptions<DemoData> basicChartOptions = new();
    private List<AdvancedChartTypeSwitcher.ChartTypeOption> basicTypes = new();

    protected override void OnInitialized()
    {
        // 生成演示數據
        GenerateDemoData();
        
        // 設置圖表類型選項
        barAreaTypes = AdvancedChartTypeSwitcher.GetBarAndAreaChartTypes();
        basicTypes = AdvancedChartTypeSwitcher.GetDefaultChartTypes();
        
        // 設置圖表選項
        SetupChartOptions();
    }

    private void GenerateDemoData()
    {
        var categories = new[] { "一月", "二月", "三月", "四月", "五月" };
        var random = new Random();
        
        demoData1 = categories.Select(category => new DemoData
        {
            Category = category,
            Value = random.Next(20, 80)
        }).ToList();

        demoData2 = categories.Select(category => new DemoData
        {
            Category = category,
            Value = random.Next(30, 90)
        }).ToList();
    }

    private void SetupChartOptions()
    {
        UpdateBarAreaChartOptions();
        UpdateBasicChartOptions();
    }

    private void UpdateBarAreaChartOptions()
    {
        barAreaChartOptions = new ApexChartOptions<DemoData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Colors = new List<string> { "#3498db", "#e74c3c" },
            DataLabels = new DataLabels { Enabled = true }
        };

        switch (barAreaChartType)
        {
            case SeriesType.Bar:
                barAreaChartOptions.Chart = new Chart
                {
                    Type = ChartType.Bar,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = barAreaStacked
                };
                barAreaChartOptions.PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        Horizontal = barAreaHorizontal,
                        BorderRadius = 5
                    }
                };
                break;
                
            case SeriesType.Area:
                barAreaChartOptions.Chart = new Chart
                {
                    Type = ChartType.Area,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = barAreaStacked
                };
                break;
        }
    }

    private void UpdateBasicChartOptions()
    {
        basicChartOptions = new ApexChartOptions<DemoData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Colors = new List<string> { "#2ecc71" },
            DataLabels = new DataLabels { Enabled = true }
        };

        switch (basicChartType)
        {
            case SeriesType.Bar:
                basicChartOptions.Chart = new Chart
                {
                    Type = ChartType.Bar,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = basicStacked
                };
                basicChartOptions.PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        Horizontal = basicHorizontal,
                        BorderRadius = 5
                    }
                };
                break;
                
            case SeriesType.Line:
                basicChartOptions.Chart = new Chart
                {
                    Type = ChartType.Line,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
                
            case SeriesType.Area:
                basicChartOptions.Chart = new Chart
                {
                    Type = ChartType.Area,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = basicStacked
                };
                break;
        }
    }

    private async Task HandleBarAreaChartChange(AdvancedChartTypeSwitcher.ChartOptionsChangeEventArgs args)
    {
        barAreaChartType = args.ChartType;
        barAreaStacked = args.IsStacked;
        barAreaHorizontal = args.IsHorizontal;
        
        UpdateBarAreaChartOptions();
        
        shouldRenderBarAreaChart = false;
        StateHasChanged();
        await Task.Delay(50);
        shouldRenderBarAreaChart = true;
        StateHasChanged();
    }

    private async Task HandleBasicChartChange(AdvancedChartTypeSwitcher.ChartOptionsChangeEventArgs args)
    {
        basicChartType = args.ChartType;
        basicStacked = args.IsStacked;
        basicHorizontal = args.IsHorizontal;
        
        UpdateBasicChartOptions();
        
        shouldRenderBasicChart = false;
        StateHasChanged();
        await Task.Delay(50);
        shouldRenderBasicChart = true;
        StateHasChanged();
    }
}
