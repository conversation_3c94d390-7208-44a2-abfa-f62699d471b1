@page "/advanced-chart-switcher-test"
@using ApexCharts
@rendermode InteractiveServer

<PageTitle>進階圖表切換功能測試</PageTitle>
<MyPageTitle Title="進階圖表切換功能測試"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="alert alert-info">
                <h5><i class="fa-solid fa-info-circle"></i> 測試說明</h5>
                <p>這個頁面用於測試新的進階圖表切換功能，包含：</p>
                <ul>
                    <li><strong>圖表類型切換</strong>：長條圖、折線圖、面積圖</li>
                    <li><strong>堆疊選項</strong>：適用於長條圖和面積圖</li>
                    <li><strong>橫式選項</strong>：適用於長條圖</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">進階圖表切換功能展示</h5>
                </div>
                <div class="card-body">
                    <AdvancedChartTypeSwitcher CurrentChartType="@currentChartType"
                                               AvailableChartTypes="@availableChartTypes"
                                               ShowBarOptions="true"
                                               ShowAreaOptions="true"
                                               IsStacked="@isStacked"
                                               IsHorizontal="@isHorizontal"
                                               OnChartOptionsChanged="@HandleChartOptionsChange" />
                    
                    @if (shouldRenderChart)
                    {
                        <ApexChart TItem="TestData"
                                   Title="測試數據圖表"
                                   Options="chartOptions">
                            @if (isMultiSeries)
                            {
                                <ApexPointSeries TItem="TestData"
                                                 Items="testDataSeries1"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 1"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                                <ApexPointSeries TItem="TestData"
                                                 Items="testDataSeries2"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 2"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                                <ApexPointSeries TItem="TestData"
                                                 Items="testDataSeries3"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 3"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                            }
                            else
                            {
                                <ApexPointSeries TItem="TestData"
                                                 Items="testDataSeries1"
                                                 SeriesType="@currentChartType"
                                                 Name="測試數據"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                            }
                        </ApexChart>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">當前設定資訊</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p><strong>圖表類型：</strong> @currentChartType</p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>堆疊模式：</strong> @(isStacked ? "啟用" : "停用")</p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>橫式模式：</strong> @(isHorizontal ? "啟用" : "停用")</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <p><strong>多系列模式：</strong> @(isMultiSeries ? "啟用" : "停用")</p>
                            <button class="btn btn-outline-secondary btn-sm" @onclick="ToggleMultiSeries">
                                切換多系列模式
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 測試數據模型
    public class TestData
    {
        public string Category { get; set; } = "";
        public decimal Value { get; set; }
    }

    // 測試數據
    private List<TestData> testDataSeries1 = new();
    private List<TestData> testDataSeries2 = new();
    private List<TestData> testDataSeries3 = new();
    private SeriesType currentChartType = SeriesType.Bar;
    private bool shouldRenderChart = true;
    private bool isStacked = false;
    private bool isHorizontal = false;
    private bool isMultiSeries = true;
    private ApexChartOptions<TestData> chartOptions = new();
    private List<AdvancedChartTypeSwitcher.ChartTypeOption> availableChartTypes = new();

    protected override void OnInitialized()
    {
        // 生成測試數據
        GenerateTestData();
        
        // 設置圖表選項
        SetupChartOptions();
        
        // 設置可用的圖表類型
        availableChartTypes = AdvancedChartTypeSwitcher.GetDefaultChartTypes();
    }

    private void GenerateTestData()
    {
        var categories = new[] { "類別A", "類別B", "類別C", "類別D", "類別E" };
        var random = new Random();
        
        testDataSeries1 = categories.Select(category => new TestData
        {
            Category = category,
            Value = random.Next(20, 80)
        }).ToList();

        testDataSeries2 = categories.Select(category => new TestData
        {
            Category = category,
            Value = random.Next(30, 90)
        }).ToList();

        testDataSeries3 = categories.Select(category => new TestData
        {
            Category = category,
            Value = random.Next(10, 70)
        }).ToList();
    }

    private void SetupChartOptions()
    {
        UpdateChartOptions();
    }

    private void UpdateChartOptions()
    {
        chartOptions = new ApexChartOptions<TestData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Colors = new List<string> { "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6" },
            DataLabels = new DataLabels { Enabled = true }
        };

        // 根據圖表類型設置不同的選項
        switch (currentChartType)
        {
            case SeriesType.Bar:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Bar,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = isStacked,
                    StackType = isStacked ? StackType.Normal : null
                };
                chartOptions.PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        Horizontal = isHorizontal,
                        BorderRadius = 5,
                        BorderRadiusApplication = BorderRadiusApplication.End
                    }
                };
                break;
                
            case SeriesType.Area:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Area,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = isStacked
                };
                break;
                
            case SeriesType.Line:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Line,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
        }
    }

    private async Task HandleChartOptionsChange(AdvancedChartTypeSwitcher.ChartOptionsChangeEventArgs args)
    {
        currentChartType = args.ChartType;
        isStacked = args.IsStacked;
        isHorizontal = args.IsHorizontal;
        
        // 更新圖表選項
        UpdateChartOptions();
        
        // 暫時隱藏圖表
        shouldRenderChart = false;
        StateHasChanged();

        // 等待一個短暫的延遲後重新顯示圖表
        await Task.Delay(50);
        shouldRenderChart = true;
        StateHasChanged();
    }

    private void ToggleMultiSeries()
    {
        isMultiSeries = !isMultiSeries;
        StateHasChanged();
    }
}
