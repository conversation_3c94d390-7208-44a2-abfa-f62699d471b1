# ChartTypeSwitcher 組件使用說明

## 概述
ChartTypeSwitcher 是一個可重用的圖表類型切換組件，允許用戶在不同的圖表類型之間動態切換。

## 功能特點
- 支持多種圖表類型切換（長條圖、折線圖、面積圖等）
- 可自定義可用的圖表類型
- 響應式設計，適配不同屏幕尺寸
- 與 AzureChart 組件完美集成

## 使用方法

### 1. 在 AzureChart 中啟用圖表切換功能

```razor
<AzureChart TContainer="UA_LibModel"
            TItem="UA_LibData"
            Title="📚 紙本圖書▸中文藏書量"
            ChartType="SeriesType.Area"
            EnableChartTypeSwitcher="true"
            AvailableChartTypes="@customChartTypes"
            ... />
```

### 2. 定義可用的圖表類型

```csharp
@code {
    private List<ChartTypeSwitcher.ChartTypeOption> customChartTypes;

    protected override void OnInitialized()
    {
        customChartTypes = new List<ChartTypeSwitcher.ChartTypeOption>
        {
            new ChartTypeSwitcher.ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeSwitcher.ChartTypeOption(SeriesType.Line, "折線圖", "fa-solid fa-chart-line"),
            new ChartTypeSwitcher.ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area")
        };
    }
}
```

### 3. 使用預設的圖表類型選項

```csharp
// 使用預設的基本圖表類型（長條圖、折線圖、面積圖）
AvailableChartTypes="@ChartTypeSwitcher.GetDefaultChartTypes()"

// 使用擴展的圖表類型（包含圓餅圖、甜甜圈圖等）
AvailableChartTypes="@ChartTypeSwitcher.GetExtendedChartTypes()"
```

## 參數說明

### AzureChart 新增參數
- `EnableChartTypeSwitcher`: bool - 是否啟用圖表切換功能
- `AvailableChartTypes`: List<ChartTypeOption> - 可用的圖表類型選項
- `OnChartTypeChanged`: EventCallback<SeriesType> - 圖表類型改變時的回調事件

### ChartTypeOption 類別
- `Type`: SeriesType - 圖表類型
- `DisplayName`: string - 顯示名稱
- `Icon`: string - Font Awesome 圖標類名

## 支持的圖表類型
- Bar (長條圖)
- Line (折線圖)
- Area (面積圖)
- Pie (圓餅圖)
- Donut (甜甜圈圖)
- Scatter (散點圖)

## 樣式自定義
組件使用 Bootstrap 5 的按鈕組樣式，可以通過 CSS 進行自定義：

```css
.btn-group .btn {
    /* 自定義按鈕樣式 */
}

.btn-primary {
    /* 選中狀態樣式 */
}

.btn-outline-primary {
    /* 未選中狀態樣式 */
}
```

## 注意事項
1. 確保項目中已引入 Font Awesome 圖標庫
2. 圖表切換時會有短暫的重新渲染過程
3. 不同圖表類型可能需要不同的數據格式，請確保數據兼容性
