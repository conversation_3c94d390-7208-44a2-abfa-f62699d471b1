@page "/chart-switcher-test"
@using ApexCharts
@rendermode InteractiveServer

<PageTitle>圖表切換功能測試</PageTitle>
<MyPageTitle Title="圖表切換功能測試"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="alert alert-info">
                <h5><i class="fa-solid fa-info-circle"></i> 測試說明</h5>
                <p>這個頁面用於測試新的圖表切換功能。你可以點擊圖表上方的按鈕來切換不同的圖表類型。</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">使用 ChartTypeSwitcher 組件的測試圖表</h5>
                </div>
                <div class="card-body">
                    <ChartTypeSwitcher CurrentChartType="@currentChartType"
                                       AvailableChartTypes="@availableChartTypes"
                                       OnChartTypeChange="@HandleChartTypeChange" />
                    
                    @if (shouldRenderChart)
                    {
                        <ApexChart TItem="TestData"
                                   Title="測試數據圖表"
                                   Options="chartOptions">
                            <ApexPointSeries TItem="TestData"
                                             Items="testData"
                                             SeriesType="@currentChartType"
                                             Name="測試數據"
                                             XValue="@(e => e.Category)"
                                             YValue="@(e => e.Value)" />
                        </ApexChart>
                    }
                </div>
            </div>
        </div>

        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">當前圖表類型資訊</h5>
                </div>
                <div class="card-body">
                    <p><strong>當前圖表類型：</strong> @currentChartType</p>
                    <p><strong>可用圖表類型：</strong></p>
                    <ul>
                        @foreach (var chartType in availableChartTypes)
                        {
                            <li>@chartType.DisplayName (@chartType.Type)</li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 測試數據模型
    public class TestData
    {
        public string Category { get; set; } = "";
        public decimal Value { get; set; }
    }

    // 測試數據
    private List<TestData> testData = new();
    private SeriesType currentChartType = SeriesType.Bar;
    private bool shouldRenderChart = true;
    private ApexChartOptions<TestData> chartOptions = new();
    private List<ChartTypeSwitcher.ChartTypeOption> availableChartTypes = new();

    protected override void OnInitialized()
    {
        // 生成測試數據
        GenerateTestData();
        
        // 設置圖表選項
        SetupChartOptions();
        
        // 設置可用的圖表類型
        availableChartTypes = ChartTypeSwitcher.GetDefaultChartTypes();
    }

    private void GenerateTestData()
    {
        var categories = new[] { "類別A", "類別B", "類別C", "類別D", "類別E" };
        var random = new Random();
        
        testData = categories.Select(category => new TestData
        {
            Category = category,
            Value = random.Next(10, 100)
        }).ToList();
    }

    private void SetupChartOptions()
    {
        chartOptions = new ApexChartOptions<TestData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6" },
            DataLabels = new DataLabels { Enabled = true }
        };
    }

    private async Task HandleChartTypeChange(SeriesType newType)
    {
        currentChartType = newType;
        
        // 暫時隱藏圖表
        shouldRenderChart = false;
        StateHasChanged();

        // 等待一個短暫的延遲後重新顯示圖表
        await Task.Delay(50);
        shouldRenderChart = true;
        StateHasChanged();
    }
}
