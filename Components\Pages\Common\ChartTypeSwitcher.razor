@using ApexCharts

<!-- 圖表類型切換按鈕組件 -->
<div class="text-center">
    <div class="btn-group" role="group">
        @foreach (var chartType in AvailableChartTypes)
        {
            <button type="button"
                    class="btn btn-sm @(CurrentChartType == chartType.Type ? "btn-primary" : "btn-outline-primary")"
                    @onclick="async () => await OnChartTypeChanged(chartType.Type)">
                <i class="@chartType.Icon"></i> @chartType.DisplayName
            </button>
        }
    </div>
</div>

@code {
    [Parameter] public SeriesType CurrentChartType { get; set; } = SeriesType.Area;
    [Parameter] public EventCallback<SeriesType> CurrentChartTypeChanged { get; set; }
    [Parameter] public List<ChartTypeOption> AvailableChartTypes { get; set; } = new();
    [Parameter] public EventCallback<SeriesType> OnChartTypeChange { get; set; }

    private async Task OnChartTypeChanged(SeriesType newType)
    {
        if (CurrentChartType != newType)
        {
            CurrentChartType = newType;
            await CurrentChartTypeChanged.InvokeAsync(newType);
            await OnChartTypeChange.InvokeAsync(newType);
        }
    }

    public class ChartTypeOption
    {
        public SeriesType Type { get; set; }
        public string DisplayName { get; set; } = "";
        public string Icon { get; set; } = "";

        public ChartTypeOption(SeriesType type, string displayName, string icon)
        {
            Type = type;
            DisplayName = displayName;
            Icon = icon;
        }
    }

    // 預設的圖表類型選項
    public static List<ChartTypeOption> GetDefaultChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area")
        };
    }

    // 擴展的圖表類型選項（包含更多類型）
    public static List<ChartTypeOption> GetExtendedChartTypes()
    {
        return new List<ChartTypeOption>
        {
            new ChartTypeOption(SeriesType.Bar, "長條圖", "fa-solid fa-chart-column"),
            new ChartTypeOption(SeriesType.Line, "折線圖", "fa-solid fa-chart-line"),
            new ChartTypeOption(SeriesType.Area, "面積圖", "fa-solid fa-chart-area"),
            new ChartTypeOption(SeriesType.Pie, "圓餅圖", "fa-solid fa-chart-pie"),
            new ChartTypeOption(SeriesType.Donut, "甜甜圈圖", "fa-solid fa-circle-notch"),
            new ChartTypeOption(SeriesType.Scatter, "散點圖", "fa-solid fa-braille")
        };
    }

    protected override void OnInitialized()
    {
        // 如果沒有提供可用的圖表類型，使用預設值
        if (AvailableChartTypes == null || !AvailableChartTypes.Any())
        {
            AvailableChartTypes = GetDefaultChartTypes();
        }
    }
}
