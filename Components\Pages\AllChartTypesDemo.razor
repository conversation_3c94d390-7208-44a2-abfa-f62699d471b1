@page "/all-chart-types-demo"
@using ApexCharts
@rendermode InteractiveServer

<PageTitle>所有圖表類型展示</PageTitle>
<MyPageTitle Title="所有圖表類型展示"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="alert alert-info">
                <h5><i class="fa-solid fa-info-circle"></i> 圖表類型總覽</h5>
                <p>這個頁面展示了系統支援的所有圖表類型，包含：</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li><strong>長條圖 (Bar)</strong> - 支援堆疊、橫式</li>
                            <li><strong>折線圖 (Line)</strong> - 支援平滑曲線、標記點</li>
                            <li><strong>面積圖 (Area)</strong> - 支援堆疊、平滑曲線</li>
                            <li><strong>圓餅圖 (Pie)</strong> - 適合分類數據</li>
                            <li><strong>甜甜圈圖 (Donut)</strong> - 支援半圓模式</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li><strong>徑向條形圖 (RadialBar)</strong> - 適合百分比數據</li>
                            <li><strong>雷達圖 (Radar)</strong> - 適合多維度比較</li>
                            <li><strong>極地面積圖 (PolarArea)</strong> - 圓形面積圖</li>
                            <li><strong>樹狀圖 (Treemap)</strong> - 適合層次數據</li>
                            <li><strong>散點圖 (Scatter)</strong> - 適合相關性分析</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">完整圖表類型切換展示</h5>
                </div>
                <div class="card-body">
                    <AdvancedChartTypeSwitcher CurrentChartType="@currentChartType"
                                               AvailableChartTypes="@allChartTypes"
                                               ShowBarOptions="true"
                                               ShowAreaOptions="true"
                                               IsStacked="@isStacked"
                                               IsHorizontal="@isHorizontal"
                                               OnChartOptionsChanged="@HandleChartOptionsChange" />
                    
                    @if (shouldRenderChart)
                    {
                        <ApexChart TItem="DemoData"
                                   Title="@GetChartTitle()"
                                   Options="chartOptions">
                            @if (IsMultiSeriesChart())
                            {
                                <ApexPointSeries TItem="DemoData"
                                                 Items="demoDataSeries1"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 1"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                                <ApexPointSeries TItem="DemoData"
                                                 Items="demoDataSeries2"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 2"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                                <ApexPointSeries TItem="DemoData"
                                                 Items="demoDataSeries3"
                                                 SeriesType="@currentChartType"
                                                 Name="系列 3"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                            }
                            else
                            {
                                <ApexPointSeries TItem="DemoData"
                                                 Items="demoDataSeries1"
                                                 SeriesType="@currentChartType"
                                                 Name="演示數據"
                                                 XValue="@(e => e.Category)"
                                                 YValue="@(e => e.Value)" />
                            }
                        </ApexChart>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">圖表類型說明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>當前設定</h6>
                            <ul>
                                <li><strong>圖表類型：</strong> @GetChartTypeDescription()</li>
                                <li><strong>堆疊模式：</strong> @(isStacked ? "啟用" : "停用")</li>
                                <li><strong>橫式模式：</strong> @(isHorizontal ? "啟用" : "停用")</li>
                                <li><strong>多系列：</strong> @(IsMultiSeriesChart() ? "啟用" : "停用")</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>適用場景</h6>
                            <p>@GetUsageScenario()</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 演示數據模型
    public class DemoData
    {
        public string Category { get; set; } = "";
        public decimal Value { get; set; }
    }

    // 演示數據
    private List<DemoData> demoDataSeries1 = new();
    private List<DemoData> demoDataSeries2 = new();
    private List<DemoData> demoDataSeries3 = new();
    private SeriesType currentChartType = SeriesType.Bar;
    private bool shouldRenderChart = true;
    private bool isStacked = false;
    private bool isHorizontal = false;
    private ApexChartOptions<DemoData> chartOptions = new();
    private List<AdvancedChartTypeSwitcher.ChartTypeOption> allChartTypes = new();

    protected override void OnInitialized()
    {
        // 生成演示數據
        GenerateDemoData();
        
        // 設置圖表選項
        SetupChartOptions();
        
        // 設置所有可用的圖表類型
        allChartTypes = AdvancedChartTypeSwitcher.GetAllChartTypes();
    }

    private void GenerateDemoData()
    {
        var categories = new[] { "產品A", "產品B", "產品C", "產品D", "產品E" };
        var random = new Random();
        
        demoDataSeries1 = categories.Select(category => new DemoData
        {
            Category = category,
            Value = random.Next(20, 80)
        }).ToList();

        demoDataSeries2 = categories.Select(category => new DemoData
        {
            Category = category,
            Value = random.Next(30, 90)
        }).ToList();

        demoDataSeries3 = categories.Select(category => new DemoData
        {
            Category = category,
            Value = random.Next(10, 70)
        }).ToList();
    }

    private void SetupChartOptions()
    {
        UpdateChartOptions();
    }

    private void UpdateChartOptions()
    {
        chartOptions = new ApexChartOptions<DemoData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Colors = new List<string> { "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6" },
            DataLabels = new DataLabels { Enabled = true }
        };

        // 根據圖表類型設置不同的選項
        switch (currentChartType)
        {
            case SeriesType.Bar:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Bar,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = isStacked,
                    StackType = isStacked ? StackType.Normal : null
                };
                chartOptions.PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        Horizontal = isHorizontal,
                        BorderRadius = 5,
                        BorderRadiusApplication = BorderRadiusApplication.End
                    }
                };
                break;
                
            case SeriesType.Area:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Area,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent",
                    Stacked = isStacked
                };
                break;
                
            case SeriesType.Line:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Line,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
                
            case SeriesType.Pie:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Pie,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
                
            case SeriesType.Donut:
                chartOptions.Chart = new Chart
                {
                    Type = ChartType.Donut,
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
                
            default:
                chartOptions.Chart = new Chart
                {
                    Toolbar = new Toolbar { Show = true },
                    Background = "transparent"
                };
                break;
        }
    }

    private async Task HandleChartOptionsChange(AdvancedChartTypeSwitcher.ChartOptionsChangeEventArgs args)
    {
        currentChartType = args.ChartType;
        isStacked = args.IsStacked;
        isHorizontal = args.IsHorizontal;
        
        // 更新圖表選項
        UpdateChartOptions();
        
        // 暫時隱藏圖表
        shouldRenderChart = false;
        StateHasChanged();

        // 等待一個短暫的延遲後重新顯示圖表
        await Task.Delay(50);
        shouldRenderChart = true;
        StateHasChanged();
    }

    private bool IsMultiSeriesChart()
    {
        return currentChartType == SeriesType.Bar || 
               currentChartType == SeriesType.Line || 
               currentChartType == SeriesType.Area ||
               currentChartType == SeriesType.Scatter;
    }

    private string GetChartTitle()
    {
        return $"{GetChartTypeDescription()} 演示";
    }

    private string GetChartTypeDescription()
    {
        return currentChartType switch
        {
            SeriesType.Bar => "長條圖",
            SeriesType.Line => "折線圖",
            SeriesType.Area => "面積圖",
            SeriesType.Pie => "圓餅圖",
            SeriesType.Donut => "甜甜圈圖",
            SeriesType.RadialBar => "徑向條形圖",
            SeriesType.Radar => "雷達圖",
            SeriesType.PolarArea => "極地面積圖",
            SeriesType.Treemap => "樹狀圖",
            SeriesType.Scatter => "散點圖",
            _ => "未知圖表類型"
        };
    }

    private string GetUsageScenario()
    {
        return currentChartType switch
        {
            SeriesType.Bar => "適合比較不同類別的數值大小，支援堆疊和橫式顯示。",
            SeriesType.Line => "適合顯示數據隨時間的變化趨勢。",
            SeriesType.Area => "適合顯示數量隨時間的累積變化，支援堆疊顯示。",
            SeriesType.Pie => "適合顯示各部分占整體的比例關係。",
            SeriesType.Donut => "類似圓餅圖，但中間有空洞，可顯示總計資訊。",
            SeriesType.RadialBar => "適合顯示百分比或進度數據。",
            SeriesType.Radar => "適合多維度數據的比較分析。",
            SeriesType.PolarArea => "適合顯示分類數據的相對大小。",
            SeriesType.Treemap => "適合顯示層次結構數據和相對大小。",
            SeriesType.Scatter => "適合分析兩個變數之間的相關性。",
            _ => "請選擇圖表類型查看說明。"
        };
    }
}
